/**
 * @license
 * Copyright 2010-2025 Three.js Authors
 * SPDX-License-Identifier: MIT
 */
import{TSL as e}from"three/webgpu";const t=e.BRDF_GGX,r=e.BRDF_Lambert,a=e.BasicShadowFilter,o=e.Break,i=e.Continue,n=e.DFGApprox,l=e.D_GGX,s=e.Discard,c=e.EPSILON,m=e.F_Schlick,p=e.Fn,d=e.INFINITY,u=e.If,g=e.Switch,h=e.Loop,x=e.NodeShaderStage,f=e.NodeType,b=e.NodeUpdateType,w=e.NodeAccess,v=e.PCFShadowFilter,S=e.PCFSoftShadowFilter,T=e.PI,_=e.PI2,y=e.Return,V=e.Schlick_to_F0,M=e.ScriptableNodeResources,D=e.ShaderNode,F=e.TBNViewMatrix,C=e.VSMShadowFilter,I=e.V_GGX_SmithCorrelated,P=e.abs,R=e.acesFilmicToneMapping,N=e.acos,B=e.add,L=e.addNodeElement,A=e.agxToneMapping,G=e.all,k=e.alphaT,O=e.and,j=e.anisotropy,W=e.anisotropyB,U=e.anisotropyT,z=e.any,q=e.append,E=e.array,Z=e.arrayBuffer,X=e.asin,Y=e.assign,H=e.atan,J=e.atan2,K=e.atomicAdd,Q=e.atomicAnd,$=e.atomicFunc,ee=e.atomicMax,te=e.atomicMin,re=e.atomicOr,ae=e.atomicStore,oe=e.atomicSub,ie=e.atomicXor,ne=e.atomicLoad,le=e.attenuationColor,se=e.attenuationDistance,ce=e.attribute,me=e.attributeArray,pe=e.backgroundBlurriness,de=e.backgroundIntensity,ue=e.backgroundRotation,ge=e.batch,he=e.bentNormalView,xe=e.billboarding,fe=e.bitAnd,be=e.bitNot,we=e.bitOr,ve=e.bitXor,Se=e.bitangentGeometry,Te=e.bitangentLocal,_e=e.bitangentView,ye=e.bitangentWorld,Ve=e.bitcast,Me=e.blendBurn,De=e.blendColor,Fe=e.blendDodge,Ce=e.blendOverlay,Ie=e.blendScreen,Pe=e.blur,Re=e.bool,Ne=e.buffer,Be=e.bufferAttribute,Le=e.bumpMap,Ae=e.burn,Ge=e.bvec2,ke=e.bvec3,Oe=e.bvec4,je=e.bypass,We=e.cache,Ue=e.call,ze=e.cameraFar,qe=e.cameraIndex,Ee=e.cameraNear,Ze=e.cameraNormalMatrix,Xe=e.cameraPosition,Ye=e.cameraProjectionMatrix,He=e.cameraProjectionMatrixInverse,Je=e.cameraViewMatrix,Ke=e.cameraWorldMatrix,Qe=e.cbrt,$e=e.cdl,et=e.ceil,tt=e.checker,rt=e.cineonToneMapping,at=e.clamp,ot=e.clearcoat,it=e.clearcoatRoughness,nt=e.code,lt=e.color,st=e.colorSpaceToWorking,ct=e.colorToDirection,mt=e.compute,pt=e.computeSkinning,dt=e.cond,ut=e.Const,gt=e.context,ht=e.convert,xt=e.convertColorSpace,ft=e.convertToTexture,bt=e.cos,wt=e.cross,vt=e.cubeTexture,St=e.dFdx,Tt=e.dFdy,_t=e.dashSize,yt=e.debug,Vt=e.decrement,Mt=e.decrementBefore,Dt=e.defaultBuildStages,Ft=e.defaultShaderStages,Ct=e.defined,It=e.degrees,Pt=e.deltaTime,Rt=e.densityFog,Nt=e.densityFogFactor,Bt=e.depth,Lt=e.depthPass,At=e.difference,Gt=e.diffuseColor,kt=e.directPointLight,Ot=e.directionToColor,jt=e.dispersion,Wt=e.distance,Ut=e.div,zt=e.dodge,qt=e.dot,Et=e.drawIndex,Zt=e.dynamicBufferAttribute,Xt=e.element,Yt=e.emissive,Ht=e.equal,Jt=e.equals,Kt=e.equirectUV,Qt=e.exp,$t=e.exp2,er=e.expression,tr=e.faceDirection,rr=e.faceForward,ar=e.faceforward,or=e.float,ir=e.floor,nr=e.fog,lr=e.fract,sr=e.frameGroup,cr=e.frameId,mr=e.frontFacing,pr=e.fwidth,dr=e.gain,ur=e.gapSize,gr=e.getConstNodeType,hr=e.getCurrentStack,xr=e.getDirection,fr=e.getDistanceAttenuation,br=e.getGeometryRoughness,wr=e.getNormalFromDepth,vr=e.getParallaxCorrectNormal,Sr=e.getRoughness,Tr=e.getScreenPosition,_r=e.getShIrradianceAt,yr=e.getTextureIndex,Vr=e.getViewPosition,Mr=e.getShadowMaterial,Dr=e.getShadowRenderObjectFunction,Fr=e.glsl,Cr=e.glslFn,Ir=e.grayscale,Pr=e.greaterThan,Rr=e.greaterThanEqual,Nr=e.hash,Br=e.highpModelNormalViewMatrix,Lr=e.highpModelViewMatrix,Ar=e.hue,Gr=e.increment,kr=e.incrementBefore,Or=e.instance,jr=e.instanceIndex,Wr=e.instancedArray,Ur=e.instancedBufferAttribute,zr=e.instancedDynamicBufferAttribute,qr=e.instancedMesh,Er=e.int,Zr=e.inverseSqrt,Xr=e.inversesqrt,Yr=e.invocationLocalIndex,Hr=e.invocationSubgroupIndex,Jr=e.ior,Kr=e.iridescence,Qr=e.iridescenceIOR,$r=e.iridescenceThickness,ea=e.ivec2,ta=e.ivec3,ra=e.ivec4,aa=e.js,oa=e.label,ia=e.length,na=e.lengthSq,la=e.lessThan,sa=e.lessThanEqual,ca=e.lightPosition,ma=e.lightShadowMatrix,pa=e.lightTargetDirection,da=e.lightTargetPosition,ua=e.lightViewPosition,ga=e.lightingContext,ha=e.lights,xa=e.linearDepth,fa=e.linearToneMapping,ba=e.localId,wa=e.globalId,va=e.log,Sa=e.log2,Ta=e.logarithmicDepthToViewZ,_a=e.loop,ya=e.luminance,Va=e.mediumpModelViewMatrix,Ma=e.mat2,Da=e.mat3,Fa=e.mat4,Ca=e.matcapUV,Ia=e.materialAO,Pa=e.materialAlphaTest,Ra=e.materialAnisotropy,Na=e.materialAnisotropyVector,Ba=e.materialAttenuationColor,La=e.materialAttenuationDistance,Aa=e.materialClearcoat,Ga=e.materialClearcoatNormal,ka=e.materialClearcoatRoughness,Oa=e.materialColor,ja=e.materialDispersion,Wa=e.materialEmissive,Ua=e.materialIOR,za=e.materialIridescence,qa=e.materialIridescenceIOR,Ea=e.materialIridescenceThickness,Za=e.materialLightMap,Xa=e.materialLineDashOffset,Ya=e.materialLineDashSize,Ha=e.materialLineGapSize,Ja=e.materialLineScale,Ka=e.materialLineWidth,Qa=e.materialMetalness,$a=e.materialNormal,eo=e.materialOpacity,to=e.materialPointSize,ro=e.materialReference,ao=e.materialReflectivity,oo=e.materialRefractionRatio,io=e.materialRotation,no=e.materialRoughness,lo=e.materialSheen,so=e.materialSheenRoughness,co=e.materialShininess,mo=e.materialSpecular,po=e.materialSpecularColor,uo=e.materialSpecularIntensity,go=e.materialSpecularStrength,ho=e.materialThickness,xo=e.materialTransmission,fo=e.max,bo=e.maxMipLevel,wo=e.metalness,vo=e.min,So=e.mix,To=e.mixElement,_o=e.mod,yo=e.modInt,Vo=e.modelDirection,Mo=e.modelNormalMatrix,Do=e.modelPosition,Fo=e.modelRadius,Co=e.modelScale,Io=e.modelViewMatrix,Po=e.modelViewPosition,Ro=e.modelViewProjection,No=e.modelWorldMatrix,Bo=e.modelWorldMatrixInverse,Lo=e.morphReference,Ao=e.mrt,Go=e.mul,ko=e.mx_aastep,Oo=e.mx_cell_noise_float,jo=e.mx_contrast,Wo=e.mx_fractal_noise_float,Uo=e.mx_fractal_noise_vec2,zo=e.mx_fractal_noise_vec3,qo=e.mx_fractal_noise_vec4,Eo=e.mx_hsvtorgb,Zo=e.mx_noise_float,Xo=e.mx_noise_vec3,Yo=e.mx_noise_vec4,Ho=e.mx_ramplr,Jo=e.mx_ramptb,Ko=e.mx_rgbtohsv,Qo=e.mx_safepower,$o=e.mx_splitlr,ei=e.mx_splittb,ti=e.mx_srgb_texture_to_lin_rec709,ri=e.mx_transform_uv,ai=e.mx_worley_noise_float,oi=e.mx_worley_noise_vec2,ii=e.mx_worley_noise_vec3,ni=e.negate,li=e.neutralToneMapping,si=e.nodeArray,ci=e.nodeImmutable,mi=e.nodeObject,pi=e.nodeObjects,di=e.nodeProxy,ui=e.normalFlat,gi=e.normalGeometry,hi=e.normalLocal,xi=e.normalMap,fi=e.normalView,bi=e.normalViewGeometry,wi=e.normalWorld,vi=e.normalWorldGeometry,Si=e.normalize,Ti=e.not,_i=e.notEqual,yi=e.numWorkgroups,Vi=e.objectDirection,Mi=e.objectGroup,Di=e.objectPosition,Fi=e.objectRadius,Ci=e.objectScale,Ii=e.objectViewPosition,Pi=e.objectWorldMatrix,Ri=e.oneMinus,Ni=e.or,Bi=e.orthographicDepthToViewZ,Li=e.oscSawtooth,Ai=e.oscSine,Gi=e.oscSquare,ki=e.oscTriangle,Oi=e.output,ji=e.outputStruct,Wi=e.overlay,Ui=e.overloadingFn,zi=e.parabola,qi=e.parallaxDirection,Ei=e.parallaxUV,Zi=e.parameter,Xi=e.pass,Yi=e.passTexture,Hi=e.pcurve,Ji=e.perspectiveDepthToViewZ,Ki=e.pmremTexture,Qi=e.pointUV,$i=e.pointWidth,en=e.positionGeometry,tn=e.positionLocal,rn=e.positionPrevious,an=e.positionView,on=e.positionViewDirection,nn=e.positionWorld,ln=e.positionWorldDirection,sn=e.posterize,cn=e.pow,mn=e.pow2,pn=e.pow3,dn=e.pow4,un=e.premultiplyAlpha,gn=e.property,hn=e.radians,xn=e.rand,fn=e.range,bn=e.rangeFog,wn=e.rangeFogFactor,vn=e.reciprocal,Sn=e.lightProjectionUV,Tn=e.reference,_n=e.referenceBuffer,yn=e.reflect,Vn=e.reflectVector,Mn=e.reflectView,Dn=e.reflector,Fn=e.refract,Cn=e.refractVector,In=e.refractView,Pn=e.reinhardToneMapping,Rn=e.remainder,Nn=e.remap,Bn=e.remapClamp,Ln=e.renderGroup,An=e.renderOutput,Gn=e.rendererReference,kn=e.rotate,On=e.rotateUV,jn=e.roughness,Wn=e.round,Un=e.rtt,zn=e.sRGBTransferEOTF,qn=e.sRGBTransferOETF,En=e.sample,Zn=e.sampler,Xn=e.samplerComparison,Yn=e.saturate,Hn=e.saturation,Jn=e.screen,Kn=e.screenCoordinate,Qn=e.screenSize,$n=e.screenUV,el=e.scriptable,tl=e.scriptableValue,rl=e.select,al=e.setCurrentStack,ol=e.shaderStages,il=e.shadow,nl=e.pointShadow,ll=e.shadowPositionWorld,sl=e.sharedUniformGroup,cl=e.shapeCircle,ml=e.sheen,pl=e.sheenRoughness,dl=e.shiftLeft,ul=e.shiftRight,gl=e.shininess,hl=e.sign,xl=e.sin,fl=e.sinc,bl=e.skinning,wl=e.smoothstep,vl=e.smoothstepElement,Sl=e.specularColor,Tl=e.specularF90,_l=e.spherizeUV,yl=e.split,Vl=e.spritesheetUV,Ml=e.sqrt,Dl=e.stack,Fl=e.step,Cl=e.storage,Il=e.storageBarrier,Pl=e.storageObject,Rl=e.storageTexture,Nl=e.string,Bl=e.struct,Ll=e.sub,Al=e.subBuild,Gl=e.subgroupIndex,kl=e.subgroupSize,Ol=e.tan,jl=e.tangentGeometry,Wl=e.tangentLocal,Ul=e.tangentView,zl=e.tangentWorld,ql=e.temp,El=e.texture,Zl=e.texture3D,Xl=e.textureBarrier,Yl=e.textureBicubic,Hl=e.textureBicubicLevel,Jl=e.textureCubeUV,Kl=e.textureLoad,Ql=e.textureSize,$l=e.textureStore,es=e.thickness,ts=e.time,rs=e.timerDelta,as=e.timerGlobal,os=e.timerLocal,is=e.toneMapping,ns=e.toneMappingExposure,ls=e.toonOutlinePass,ss=e.transformDirection,cs=e.transformNormal,ms=e.transformNormalToView,ps=e.transformedClearcoatNormalView,ds=e.transformedNormalView,us=e.transformedNormalWorld,gs=e.transmission,hs=e.transpose,xs=e.triNoise3D,fs=e.triplanarTexture,bs=e.triplanarTextures,ws=e.trunc,vs=e.tslFn,Ss=e.uint,Ts=e.uniform,_s=e.uniformCubeTexture,ys=e.uniformArray,Vs=e.uniformGroup,Ms=e.uniformTexture,Ds=e.uniforms,Fs=e.unpremultiplyAlpha,Cs=e.userData,Is=e.uv,Ps=e.uvec2,Rs=e.uvec3,Ns=e.uvec4,Bs=e.Var,Ls=e.varying,As=e.varyingProperty,Gs=e.vec2,ks=e.vec3,Os=e.vec4,js=e.vectorComponents,Ws=e.velocity,Us=e.vertexColor,zs=e.vertexIndex,qs=e.vibrance,Es=e.viewZToLogarithmicDepth,Zs=e.viewZToOrthographicDepth,Xs=e.viewZToPerspectiveDepth,Ys=e.viewport,Hs=e.viewportBottomLeft,Js=e.viewportCoordinate,Ks=e.viewportDepthTexture,Qs=e.viewportLinearDepth,$s=e.viewportMipTexture,ec=e.viewportResolution,tc=e.viewportSafeUV,rc=e.viewportSharedTexture,ac=e.viewportSize,oc=e.viewportTexture,ic=e.viewportTopLeft,nc=e.viewportUV,lc=e.wgsl,sc=e.wgslFn,cc=e.workgroupArray,mc=e.workgroupBarrier,pc=e.workgroupId,dc=e.workingToColorSpace,uc=e.xor;export{t as BRDF_GGX,r as BRDF_Lambert,a as BasicShadowFilter,o as Break,ut as Const,i as Continue,n as DFGApprox,l as D_GGX,s as Discard,c as EPSILON,m as F_Schlick,p as Fn,d as INFINITY,u as If,h as Loop,w as NodeAccess,x as NodeShaderStage,f as NodeType,b as NodeUpdateType,v as PCFShadowFilter,S as PCFSoftShadowFilter,T as PI,_ as PI2,y as Return,V as Schlick_to_F0,M as ScriptableNodeResources,D as ShaderNode,g as Switch,F as TBNViewMatrix,C as VSMShadowFilter,I as V_GGX_SmithCorrelated,Bs as Var,P as abs,R as acesFilmicToneMapping,N as acos,B as add,L as addNodeElement,A as agxToneMapping,G as all,k as alphaT,O as and,j as anisotropy,W as anisotropyB,U as anisotropyT,z as any,q as append,E as array,Z as arrayBuffer,X as asin,Y as assign,H as atan,J as atan2,K as atomicAdd,Q as atomicAnd,$ as atomicFunc,ne as atomicLoad,ee as atomicMax,te as atomicMin,re as atomicOr,ae as atomicStore,oe as atomicSub,ie as atomicXor,le as attenuationColor,se as attenuationDistance,ce as attribute,me as attributeArray,pe as backgroundBlurriness,de as backgroundIntensity,ue as backgroundRotation,ge as batch,he as bentNormalView,xe as billboarding,fe as bitAnd,be as bitNot,we as bitOr,ve as bitXor,Se as bitangentGeometry,Te as bitangentLocal,_e as bitangentView,ye as bitangentWorld,Ve as bitcast,Me as blendBurn,De as blendColor,Fe as blendDodge,Ce as blendOverlay,Ie as blendScreen,Pe as blur,Re as bool,Ne as buffer,Be as bufferAttribute,Le as bumpMap,Ae as burn,Ge as bvec2,ke as bvec3,Oe as bvec4,je as bypass,We as cache,Ue as call,ze as cameraFar,qe as cameraIndex,Ee as cameraNear,Ze as cameraNormalMatrix,Xe as cameraPosition,Ye as cameraProjectionMatrix,He as cameraProjectionMatrixInverse,Je as cameraViewMatrix,Ke as cameraWorldMatrix,Qe as cbrt,$e as cdl,et as ceil,tt as checker,rt as cineonToneMapping,at as clamp,ot as clearcoat,it as clearcoatRoughness,nt as code,lt as color,st as colorSpaceToWorking,ct as colorToDirection,mt as compute,pt as computeSkinning,dt as cond,gt as context,ht as convert,xt as convertColorSpace,ft as convertToTexture,bt as cos,wt as cross,vt as cubeTexture,St as dFdx,Tt as dFdy,_t as dashSize,yt as debug,Vt as decrement,Mt as decrementBefore,Dt as defaultBuildStages,Ft as defaultShaderStages,Ct as defined,It as degrees,Pt as deltaTime,Rt as densityFog,Nt as densityFogFactor,Bt as depth,Lt as depthPass,At as difference,Gt as diffuseColor,kt as directPointLight,Ot as directionToColor,jt as dispersion,Wt as distance,Ut as div,zt as dodge,qt as dot,Et as drawIndex,Zt as dynamicBufferAttribute,Xt as element,Yt as emissive,Ht as equal,Jt as equals,Kt as equirectUV,Qt as exp,$t as exp2,er as expression,tr as faceDirection,rr as faceForward,ar as faceforward,or as float,ir as floor,nr as fog,lr as fract,sr as frameGroup,cr as frameId,mr as frontFacing,pr as fwidth,dr as gain,ur as gapSize,gr as getConstNodeType,hr as getCurrentStack,xr as getDirection,fr as getDistanceAttenuation,br as getGeometryRoughness,wr as getNormalFromDepth,vr as getParallaxCorrectNormal,Sr as getRoughness,Tr as getScreenPosition,_r as getShIrradianceAt,Mr as getShadowMaterial,Dr as getShadowRenderObjectFunction,yr as getTextureIndex,Vr as getViewPosition,wa as globalId,Fr as glsl,Cr as glslFn,Ir as grayscale,Pr as greaterThan,Rr as greaterThanEqual,Nr as hash,Br as highpModelNormalViewMatrix,Lr as highpModelViewMatrix,Ar as hue,Gr as increment,kr as incrementBefore,Or as instance,jr as instanceIndex,Wr as instancedArray,Ur as instancedBufferAttribute,zr as instancedDynamicBufferAttribute,qr as instancedMesh,Er as int,Zr as inverseSqrt,Xr as inversesqrt,Yr as invocationLocalIndex,Hr as invocationSubgroupIndex,Jr as ior,Kr as iridescence,Qr as iridescenceIOR,$r as iridescenceThickness,ea as ivec2,ta as ivec3,ra as ivec4,aa as js,oa as label,ia as length,na as lengthSq,la as lessThan,sa as lessThanEqual,ca as lightPosition,Sn as lightProjectionUV,ma as lightShadowMatrix,pa as lightTargetDirection,da as lightTargetPosition,ua as lightViewPosition,ga as lightingContext,ha as lights,xa as linearDepth,fa as linearToneMapping,ba as localId,va as log,Sa as log2,Ta as logarithmicDepthToViewZ,_a as loop,ya as luminance,Ma as mat2,Da as mat3,Fa as mat4,Ca as matcapUV,Ia as materialAO,Pa as materialAlphaTest,Ra as materialAnisotropy,Na as materialAnisotropyVector,Ba as materialAttenuationColor,La as materialAttenuationDistance,Aa as materialClearcoat,Ga as materialClearcoatNormal,ka as materialClearcoatRoughness,Oa as materialColor,ja as materialDispersion,Wa as materialEmissive,Ua as materialIOR,za as materialIridescence,qa as materialIridescenceIOR,Ea as materialIridescenceThickness,Za as materialLightMap,Xa as materialLineDashOffset,Ya as materialLineDashSize,Ha as materialLineGapSize,Ja as materialLineScale,Ka as materialLineWidth,Qa as materialMetalness,$a as materialNormal,eo as materialOpacity,to as materialPointSize,ro as materialReference,ao as materialReflectivity,oo as materialRefractionRatio,io as materialRotation,no as materialRoughness,lo as materialSheen,so as materialSheenRoughness,co as materialShininess,mo as materialSpecular,po as materialSpecularColor,uo as materialSpecularIntensity,go as materialSpecularStrength,ho as materialThickness,xo as materialTransmission,fo as max,bo as maxMipLevel,Va as mediumpModelViewMatrix,wo as metalness,vo as min,So as mix,To as mixElement,_o as mod,yo as modInt,Vo as modelDirection,Mo as modelNormalMatrix,Do as modelPosition,Fo as modelRadius,Co as modelScale,Io as modelViewMatrix,Po as modelViewPosition,Ro as modelViewProjection,No as modelWorldMatrix,Bo as modelWorldMatrixInverse,Lo as morphReference,Ao as mrt,Go as mul,ko as mx_aastep,Oo as mx_cell_noise_float,jo as mx_contrast,Wo as mx_fractal_noise_float,Uo as mx_fractal_noise_vec2,zo as mx_fractal_noise_vec3,qo as mx_fractal_noise_vec4,Eo as mx_hsvtorgb,Zo as mx_noise_float,Xo as mx_noise_vec3,Yo as mx_noise_vec4,Ho as mx_ramplr,Jo as mx_ramptb,Ko as mx_rgbtohsv,Qo as mx_safepower,$o as mx_splitlr,ei as mx_splittb,ti as mx_srgb_texture_to_lin_rec709,ri as mx_transform_uv,ai as mx_worley_noise_float,oi as mx_worley_noise_vec2,ii as mx_worley_noise_vec3,ni as negate,li as neutralToneMapping,si as nodeArray,ci as nodeImmutable,mi as nodeObject,pi as nodeObjects,di as nodeProxy,ui as normalFlat,gi as normalGeometry,hi as normalLocal,xi as normalMap,fi as normalView,bi as normalViewGeometry,wi as normalWorld,vi as normalWorldGeometry,Si as normalize,Ti as not,_i as notEqual,yi as numWorkgroups,Vi as objectDirection,Mi as objectGroup,Di as objectPosition,Fi as objectRadius,Ci as objectScale,Ii as objectViewPosition,Pi as objectWorldMatrix,Ri as oneMinus,Ni as or,Bi as orthographicDepthToViewZ,Li as oscSawtooth,Ai as oscSine,Gi as oscSquare,ki as oscTriangle,Oi as output,ji as outputStruct,Wi as overlay,Ui as overloadingFn,zi as parabola,qi as parallaxDirection,Ei as parallaxUV,Zi as parameter,Xi as pass,Yi as passTexture,Hi as pcurve,Ji as perspectiveDepthToViewZ,Ki as pmremTexture,nl as pointShadow,Qi as pointUV,$i as pointWidth,en as positionGeometry,tn as positionLocal,rn as positionPrevious,an as positionView,on as positionViewDirection,nn as positionWorld,ln as positionWorldDirection,sn as posterize,cn as pow,mn as pow2,pn as pow3,dn as pow4,un as premultiplyAlpha,gn as property,hn as radians,xn as rand,fn as range,bn as rangeFog,wn as rangeFogFactor,vn as reciprocal,Tn as reference,_n as referenceBuffer,yn as reflect,Vn as reflectVector,Mn as reflectView,Dn as reflector,Fn as refract,Cn as refractVector,In as refractView,Pn as reinhardToneMapping,Rn as remainder,Nn as remap,Bn as remapClamp,Ln as renderGroup,An as renderOutput,Gn as rendererReference,kn as rotate,On as rotateUV,jn as roughness,Wn as round,Un as rtt,zn as sRGBTransferEOTF,qn as sRGBTransferOETF,En as sample,Zn as sampler,Xn as samplerComparison,Yn as saturate,Hn as saturation,Jn as screen,Kn as screenCoordinate,Qn as screenSize,$n as screenUV,el as scriptable,tl as scriptableValue,rl as select,al as setCurrentStack,ol as shaderStages,il as shadow,ll as shadowPositionWorld,cl as shapeCircle,sl as sharedUniformGroup,ml as sheen,pl as sheenRoughness,dl as shiftLeft,ul as shiftRight,gl as shininess,hl as sign,xl as sin,fl as sinc,bl as skinning,wl as smoothstep,vl as smoothstepElement,Sl as specularColor,Tl as specularF90,_l as spherizeUV,yl as split,Vl as spritesheetUV,Ml as sqrt,Dl as stack,Fl as step,Cl as storage,Il as storageBarrier,Pl as storageObject,Rl as storageTexture,Nl as string,Bl as struct,Ll as sub,Al as subBuild,Gl as subgroupIndex,kl as subgroupSize,Ol as tan,jl as tangentGeometry,Wl as tangentLocal,Ul as tangentView,zl as tangentWorld,ql as temp,El as texture,Zl as texture3D,Xl as textureBarrier,Yl as textureBicubic,Hl as textureBicubicLevel,Jl as textureCubeUV,Kl as textureLoad,Ql as textureSize,$l as textureStore,es as thickness,ts as time,rs as timerDelta,as as timerGlobal,os as timerLocal,is as toneMapping,ns as toneMappingExposure,ls as toonOutlinePass,ss as transformDirection,cs as transformNormal,ms as transformNormalToView,ps as transformedClearcoatNormalView,ds as transformedNormalView,us as transformedNormalWorld,gs as transmission,hs as transpose,xs as triNoise3D,fs as triplanarTexture,bs as triplanarTextures,ws as trunc,vs as tslFn,Ss as uint,Ts as uniform,ys as uniformArray,_s as uniformCubeTexture,Vs as uniformGroup,Ms as uniformTexture,Ds as uniforms,Fs as unpremultiplyAlpha,Cs as userData,Is as uv,Ps as uvec2,Rs as uvec3,Ns as uvec4,Ls as varying,As as varyingProperty,Gs as vec2,ks as vec3,Os as vec4,js as vectorComponents,Ws as velocity,Us as vertexColor,zs as vertexIndex,qs as vibrance,Es as viewZToLogarithmicDepth,Zs as viewZToOrthographicDepth,Xs as viewZToPerspectiveDepth,Ys as viewport,Hs as viewportBottomLeft,Js as viewportCoordinate,Ks as viewportDepthTexture,Qs as viewportLinearDepth,$s as viewportMipTexture,ec as viewportResolution,tc as viewportSafeUV,rc as viewportSharedTexture,ac as viewportSize,oc as viewportTexture,ic as viewportTopLeft,nc as viewportUV,lc as wgsl,sc as wgslFn,cc as workgroupArray,mc as workgroupBarrier,pc as workgroupId,dc as workingToColorSpace,uc as xor};
